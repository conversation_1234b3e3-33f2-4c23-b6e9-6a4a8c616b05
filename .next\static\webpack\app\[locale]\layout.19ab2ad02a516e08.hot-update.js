"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/components/ui/language-switcher.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/language-switcher.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageSwitcher: function() { return /* binding */ LanguageSwitcher; },\n/* harmony export */   useLanguageSwitcher: function() { return /* binding */ useLanguageSwitcher; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/i18n */ \"(app-pages-browser)/./src/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageSwitcher,useLanguageSwitcher auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction LanguageSwitcher(param) {\n    let { variant = \"default\", showFlags = true, showNativeNames = true, className } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const currentLocale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 确保组件在客户端渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 处理语言切换\n    const handleLanguageChange = (newLocale)=>{\n        if (newLocale === currentLocale) {\n            setIsOpen(false);\n            return;\n        }\n        // 保存语言偏好到localStorage\n        if (true) {\n            localStorage.setItem(\"preferred-locale\", newLocale);\n        }\n        // 构建新的路径\n        const segments = pathname.split(\"/\");\n        segments[1] = newLocale; // 替换语言段\n        const newPath = segments.join(\"/\");\n        // 导航到新语言页面\n        router.push(newPath);\n        setIsOpen(false);\n    };\n    // 获取当前语言配置\n    const currentConfig = _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig[currentLocale];\n    // 如果还没有在客户端渲染，显示占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-10 h-10 bg-muted rounded-md animate-pulse\", className)\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    // 移动端变体\n    if (variant === \"mobile\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"w-full justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: currentConfig.flag\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 27\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: showNativeNames ? currentConfig.nativeName : currentConfig.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform\", isOpen && \"rotate-180\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 z-40 bg-black/20 backdrop-blur-sm\",\n                            onClick: ()=>setIsOpen(false)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"absolute top-full left-0 right-0 mt-2 z-50 p-2 max-h-64 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: _i18n__WEBPACK_IMPORTED_MODULE_6__.locales.map((locale)=>{\n                                    const config = _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig[locale];\n                                    const isActive = locale === currentLocale;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLanguageChange(locale),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full flex items-center justify-between p-3 rounded-md text-left transition-colors\", \"hover:bg-accent/50\", isActive && \"bg-mystical-50 text-mystical-700 dark:bg-mystical-900/20 dark:text-mystical-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: config.flag\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: showNativeNames ? config.nativeName : config.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: config.region\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 23\n                                            }, this),\n                                            isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-mystical-600\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, locale, true, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this);\n    }\n    // 紧凑变体\n    if (variant === \"compact\") {\n        // 为紧凑模式创建简洁的显示文本\n        const getCompactDisplayText = (locale)=>{\n            switch(locale){\n                case \"zh-CN\":\n                    return \"简\";\n                case \"zh-TW\":\n                    return \"繁\";\n                case \"en\":\n                    return \"EN\";\n                case \"es\":\n                    return \"ES\";\n                case \"pt\":\n                    return \"PT\";\n                case \"hi\":\n                    return \"हि\";\n                case \"ja\":\n                    return \"日\";\n                default:\n                    return locale.toUpperCase().slice(0, 2);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"h-8 px-2 gap-1\",\n                    children: [\n                        showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: currentConfig.flag\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-medium\",\n                            children: getCompactDisplayText(currentLocale)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-3 w-3\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 z-40\",\n                            onClick: ()=>setIsOpen(false)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"absolute top-full right-0 mt-1 z-50 p-1 min-w-[200px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-0.5\",\n                                children: _i18n__WEBPACK_IMPORTED_MODULE_6__.locales.map((locale)=>{\n                                    const config = _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig[locale];\n                                    const isActive = locale === currentLocale;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLanguageChange(locale),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full flex items-center space-x-2 p-2 rounded text-left text-sm transition-colors\", \"hover:bg-accent/50\", isActive && \"bg-mystical-50 text-mystical-700 dark:bg-mystical-900/20\"),\n                                        children: [\n                                            showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: config.flag\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: showNativeNames ? config.nativeName : config.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 23\n                                            }, this),\n                                            isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"ml-auto h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 36\n                                            }, this)\n                                        ]\n                                    }, locale, true, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    // 默认变体（桌面端）\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"outline\",\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: currentConfig.flag\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 23\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: showNativeNames ? currentConfig.nativeName : currentConfig.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform\", isOpen && \"rotate-180\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40\",\n                        onClick: ()=>setIsOpen(false)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"absolute top-full right-0 mt-2 z-50 p-2 min-w-[280px] max-h-80 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: _i18n__WEBPACK_IMPORTED_MODULE_6__.locales.map((locale)=>{\n                                const config = _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig[locale];\n                                const isActive = locale === currentLocale;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleLanguageChange(locale),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full flex items-center justify-between p-3 rounded-md text-left transition-colors\", \"hover:bg-accent/50\", isActive && \"bg-mystical-50 text-mystical-700 dark:bg-mystical-900/20 dark:text-mystical-300\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg\",\n                                                    children: config.flag\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: showNativeNames ? config.nativeName : config.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                config.region,\n                                                                \" • \",\n                                                                config.population\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 21\n                                        }, this),\n                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-mystical-600\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, locale, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageSwitcher, \"VJ4GHJkkNLLiYEBHfU9jLaSY5H0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale\n    ];\n});\n_c = LanguageSwitcher;\n// 语言切换Hook - 用于程序化语言切换\nfunction useLanguageSwitcher() {\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const currentLocale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const switchLanguage = (newLocale)=>{\n        if (newLocale === currentLocale) return;\n        // 保存语言偏好\n        if (true) {\n            localStorage.setItem(\"preferred-locale\", newLocale);\n        }\n        // 构建新路径\n        const segments = pathname.split(\"/\");\n        segments[1] = newLocale;\n        const newPath = segments.join(\"/\");\n        router.push(newPath);\n    };\n    return {\n        currentLocale,\n        switchLanguage,\n        availableLocales: _i18n__WEBPACK_IMPORTED_MODULE_6__.locales,\n        languageConfig: _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig\n    };\n}\n_s1(useLanguageSwitcher, \"BzQ60aO0Rh67ubEIDNScXk43h74=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"LanguageSwitcher\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/language-switcher.tsx\n"));

/***/ })

});