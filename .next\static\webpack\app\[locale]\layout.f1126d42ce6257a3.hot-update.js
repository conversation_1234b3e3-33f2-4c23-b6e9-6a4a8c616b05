"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/components/ui/language-switcher.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/language-switcher.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageSwitcher: function() { return /* binding */ LanguageSwitcher; },\n/* harmony export */   useLanguageSwitcher: function() { return /* binding */ useLanguageSwitcher; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/i18n */ \"(app-pages-browser)/./src/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageSwitcher,useLanguageSwitcher auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction LanguageSwitcher(param) {\n    let { variant = \"default\", showFlags = true, showNativeNames = true, className } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const currentLocale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 确保组件在客户端渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 处理语言切换\n    const handleLanguageChange = (newLocale)=>{\n        if (newLocale === currentLocale) {\n            setIsOpen(false);\n            return;\n        }\n        // 保存语言偏好到localStorage\n        if (true) {\n            localStorage.setItem(\"preferred-locale\", newLocale);\n        }\n        // 构建新的路径\n        const segments = pathname.split(\"/\");\n        segments[1] = newLocale; // 替换语言段\n        const newPath = segments.join(\"/\");\n        // 导航到新语言页面\n        router.push(newPath);\n        setIsOpen(false);\n    };\n    // 获取当前语言配置\n    const currentConfig = _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig[currentLocale];\n    // 如果还没有在客户端渲染，显示占位符\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-10 h-10 bg-muted rounded-md animate-pulse\", className)\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    // 移动端变体\n    if (variant === \"mobile\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"w-full justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: currentConfig.flag\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 27\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: showNativeNames ? currentConfig.nativeName : currentConfig.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform\", isOpen && \"rotate-180\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 z-40 bg-black/20 backdrop-blur-sm\",\n                            onClick: ()=>setIsOpen(false)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"absolute top-full left-0 right-0 mt-2 z-50 p-2 max-h-64 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: _i18n__WEBPACK_IMPORTED_MODULE_6__.locales.map((locale)=>{\n                                    const config = _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig[locale];\n                                    const isActive = locale === currentLocale;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLanguageChange(locale),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full flex items-center justify-between p-3 rounded-md text-left transition-colors\", \"hover:bg-accent/50\", isActive && \"bg-mystical-50 text-mystical-700 dark:bg-mystical-900/20 dark:text-mystical-300\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: config.flag\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: showNativeNames ? config.nativeName : config.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: config.region\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 23\n                                            }, this),\n                                            isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-mystical-600\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, locale, true, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this);\n    }\n    // 紧凑变体\n    if (variant === \"compact\") {\n        // 为紧凑模式创建简洁的显示文本\n        const getCompactDisplayText = (locale)=>{\n            switch(locale){\n                case \"zh-CN\":\n                    return \"简\";\n                case \"zh-TW\":\n                    return \"繁\";\n                case \"en\":\n                    return \"EN\";\n                case \"es\":\n                    return \"ES\";\n                case \"pt\":\n                    return \"PT\";\n                case \"hi\":\n                    return \"हि\";\n                case \"ja\":\n                    return \"日\";\n                default:\n                    return locale.toUpperCase().slice(0, 2);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"h-8 px-2 flex items-center\",\n                    children: [\n                        showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-1\",\n                            children: currentConfig.flag\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs font-medium\",\n                            children: getCompactDisplayText(currentLocale)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"ml-1 h-3 w-3\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 z-40\",\n                            onClick: ()=>setIsOpen(false)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"absolute top-full right-0 mt-1 z-50 p-1 min-w-[200px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-0.5\",\n                                children: _i18n__WEBPACK_IMPORTED_MODULE_6__.locales.map((locale)=>{\n                                    const config = _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig[locale];\n                                    const isActive = locale === currentLocale;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleLanguageChange(locale),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full flex items-center space-x-2 p-2 rounded text-left text-sm transition-colors\", \"hover:bg-accent/50\", isActive && \"bg-mystical-50 text-mystical-700 dark:bg-mystical-900/20\"),\n                                        children: [\n                                            showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: config.flag\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: showNativeNames ? config.nativeName : config.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 23\n                                            }, this),\n                                            isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"ml-auto h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 36\n                                            }, this)\n                                        ]\n                                    }, locale, true, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this);\n    }\n    // 默认变体（桌面端）\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"outline\",\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: currentConfig.flag\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 23\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: showNativeNames ? currentConfig.nativeName : currentConfig.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform\", isOpen && \"rotate-180\")\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40\",\n                        onClick: ()=>setIsOpen(false)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"absolute top-full right-0 mt-2 z-50 p-2 min-w-[280px] max-h-80 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: _i18n__WEBPACK_IMPORTED_MODULE_6__.locales.map((locale)=>{\n                                const config = _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig[locale];\n                                const isActive = locale === currentLocale;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleLanguageChange(locale),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full flex items-center justify-between p-3 rounded-md text-left transition-colors\", \"hover:bg-accent/50\", isActive && \"bg-mystical-50 text-mystical-700 dark:bg-mystical-900/20 dark:text-mystical-300\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                showFlags && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg\",\n                                                    children: config.flag\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: showNativeNames ? config.nativeName : config.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                config.region,\n                                                                \" • \",\n                                                                config.population\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 21\n                                        }, this),\n                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-mystical-600\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, locale, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\language-switcher.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageSwitcher, \"VJ4GHJkkNLLiYEBHfU9jLaSY5H0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale\n    ];\n});\n_c = LanguageSwitcher;\n// 语言切换Hook - 用于程序化语言切换\nfunction useLanguageSwitcher() {\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const currentLocale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale)();\n    const switchLanguage = (newLocale)=>{\n        if (newLocale === currentLocale) return;\n        // 保存语言偏好\n        if (true) {\n            localStorage.setItem(\"preferred-locale\", newLocale);\n        }\n        // 构建新路径\n        const segments = pathname.split(\"/\");\n        segments[1] = newLocale;\n        const newPath = segments.join(\"/\");\n        router.push(newPath);\n    };\n    return {\n        currentLocale,\n        switchLanguage,\n        availableLocales: _i18n__WEBPACK_IMPORTED_MODULE_6__.locales,\n        languageConfig: _i18n__WEBPACK_IMPORTED_MODULE_6__.languageConfig\n    };\n}\n_s1(useLanguageSwitcher, \"BzQ60aO0Rh67ubEIDNScXk43h74=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useLocale\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"LanguageSwitcher\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/language-switcher.tsx\n"));

/***/ })

});