// 基础类型定义

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  locale: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TestResult {
  id: string;
  userId?: string;
  testType: TestType;
  answers: TestAnswer[];
  result: TestResultData;
  shareToken?: string;
  createdAt: Date;
}

export interface TestAnswer {
  questionId: string;
  answer: string | number | string[];
  timestamp: Date;
}

export interface TestResultData {
  title: string;
  description: string;
  score?: number;
  category: string;
  insights: string[];
  recommendations: string[];
  accuracy: number;
}

export type TestType = 
  | 'tarot'
  | 'astrology'
  | 'numerology'
  | 'crystal'
  | 'palmistry'
  | 'dreams';

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: string;
  category: string;
  tags: string[];
  image?: string;
  locale: string;
  published: boolean;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  image?: string;
  locale: string;
}

export interface TarotCard {
  id: string;
  name: string;
  slug: string;
  arcana: 'major' | 'minor';
  suit?: 'cups' | 'wands' | 'swords' | 'pentacles';
  number?: number;
  image: string;
  meanings: {
    upright: string[];
    reversed: string[];
  };
  description: string;
  keywords: string[];
}

export interface ZodiacSign {
  id: string;
  name: string;
  slug: string;
  symbol: string;
  element: 'fire' | 'earth' | 'air' | 'water';
  quality: 'cardinal' | 'fixed' | 'mutable';
  rulingPlanet: string;
  dateRange: {
    start: string; // MM-DD format
    end: string;   // MM-DD format
  };
  traits: {
    positive: string[];
    negative: string[];
  };
  compatibility: string[];
  description: string;
}

export interface NumerologyNumber {
  number: number;
  name: string;
  meaning: string;
  traits: string[];
  challenges: string[];
  opportunities: string[];
}

export interface Crystal {
  id: string;
  name: string;
  slug: string;
  color: string;
  chakra: string[];
  properties: string[];
  uses: string[];
  image: string;
  description: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 表单类型
export interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface NewsletterForm {
  email: string;
  locale: string;
}

// SEO类型
export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  locale?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

// 导航类型
export interface NavigationItem {
  label: string;
  href: string;
  icon?: string;
  children?: NavigationItem[];
  external?: boolean;
}

// 测试问题类型
export interface TestQuestion {
  id: string;
  type: 'single' | 'multiple' | 'scale' | 'text';
  question: string;
  options?: TestOption[];
  required: boolean;
  order: number;
}

export interface TestOption {
  id: string;
  text: string;
  value: string | number;
  image?: string;
}

// 统计类型
export interface SiteStats {
  totalUsers: number;
  totalTests: number;
  totalPosts: number;
  averageAccuracy: number;
  popularTests: Array<{
    type: TestType;
    count: number;
  }>;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// 主题类型
export type Theme = 'light' | 'dark' | 'system';

// 语言类型
export type Locale = 'en' | 'zh-CN' | 'zh-TW' | 'es' | 'pt' | 'hi' | 'ja';

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// 排序类型
export type SortOrder = 'asc' | 'desc';

export interface SortOption {
  field: string;
  order: SortOrder;
  label: string;
}

// 过滤类型
export interface FilterOption {
  key: string;
  value: string | number | boolean;
  label: string;
}

// 分页类型
export interface PaginationOptions {
  page: number;
  limit: number;
  sort?: SortOption;
  filters?: FilterOption[];
}

// 搜索类型
export interface SearchOptions extends PaginationOptions {
  query: string;
  category?: string;
  tags?: string[];
}

// 缓存类型
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  tags?: string[];
  revalidate?: boolean;
}

// 分析类型
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp: Date;
  userId?: string;
  sessionId: string;
}
